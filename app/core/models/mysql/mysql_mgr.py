import aiomysql

from app import app

__all__ = ["mysql_connection_pool_factory", "get_shared_mysql_pool"]

# Global shared connection pool for Celery tasks
_shared_mysql_pool = None


async def mysql_connection_pool_factory(**kwargs):
    """ This method is used to create MySQL pool creation """

    minsize = kwargs.get('minsize', app.config.MYSQL_CONNECTION_POOL_MINIMUM_SIZE)
    maxsize = kwargs.get('maxsize', app.config.MYSQL_CONNECTION_POOL_MAXIMUM_SIZE)
    host = kwargs.get('host', app.config.MYSQL_DATABASE_HOST)
    user = kwargs.get('user', app.config.MYSQL_DATABASE_USER)
    password = kwargs.get('password', app.config.MYSQL_DATABASE_PASSWORD)
    db = kwargs.get('db', app.config.MYSQL_DATABASE_NAME)
    pool_recycle = kwargs.get('pool_recycle', app.config.MYSQL_CONNECTION_MAX_POOL_RECYCLE_TIME)

    return await aiomysql.create_pool(
        minsize=minsize,
        maxsize=maxsize,
        host=host,
        user=user,
        password=password,
        db=db,
        pool_recycle=pool_recycle,
        cursorclass=aiomysql.DictCursor,
        autocommit=True,
    )


async def get_shared_mysql_pool():
    """
    Get or create a shared MySQL connection pool for Celery tasks.
    This prevents each task from creating its own pool and exhausting connections.
    """
    global _shared_mysql_pool

    if _shared_mysql_pool is None or _shared_mysql_pool.closed:
        # Create a larger shared pool for concurrent Celery tasks
        # Increase pool size to handle more concurrent tasks
        _shared_mysql_pool = await mysql_connection_pool_factory(
            minsize=10,  # Increased from default 5
            maxsize=50,  # Increased from default 10 to handle more concurrent tasks
        )

    return _shared_mysql_pool
